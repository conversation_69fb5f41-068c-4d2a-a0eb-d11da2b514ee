"""
个性化APP路由模块
"""

from typing import List, Dict, Any, Optional
from fastapi import APIRouter, HTTPException, Request, UploadFile, File, Form
from fastapi.responses import HTMLResponse, FileResponse, JSONResponse
from fastapi.templating import Jinja2Templates
from pydantic import BaseModel
import json
import os
from datetime import datetime
from loguru import logger

router = APIRouter()
templates = Jinja2Templates(directory="templates")

# 数据模型
class AppMetadata(BaseModel):
    """APP元数据"""
    id: str
    name: str
    description: str
    icon: str = "fas fa-cube"
    category: str = "其他"
    tags: List[str] = []
    created_at: str
    updated_at: str
    version: str = "1.0.0"
    author: str = "用户"
    pages: List[str] = ["index.html"]
    access_count: int = 0
    is_public: bool = True

class AppBuildRequest(BaseModel):
    """APP构建请求"""
    app_id: Optional[str] = None
    name: str
    description: str
    requirements: str
    iteration_history: List[Dict[str, Any]] = []

class AppUpdateRequest(BaseModel):
    """APP更新请求"""
    app_id: str
    page_name: str
    content: str

class AppMetadataUpdateRequest(BaseModel):
    """APP元数据更新请求"""
    app_id: str
    name: str
    description: str

class AppAccessLog(BaseModel):
    """APP访问日志"""
    app_id: str
    timestamp: str
    ip_address: str
    user_agent: str
    page: str

# APP商城主页
@router.get("/store", response_class=HTMLResponse)
async def app_store(request: Request):
    """个性化APP商城主页"""
    return templates.TemplateResponse("app_store.html", {"request": request})

# APP构建器页面
@router.get("/builder", response_class=HTMLResponse)
async def app_builder(request: Request, app_id: Optional[str] = None):
    """APP构建器页面"""
    return templates.TemplateResponse("app_builder.html", {
        "request": request,
        "app_id": app_id
    })

# 获取APP列表
@router.get("/api/apps")
async def get_apps(category: Optional[str] = None, search: Optional[str] = None):
    """获取APP列表"""
    try:
        from ...custom_app.app_manager import AppManager
        app_manager = AppManager()
        
        apps = app_manager.get_apps(category=category, search=search)
        return {
            "success": True,
            "apps": apps,
            "count": len(apps)
        }
    except Exception as e:
        logger.error(f"获取APP列表失败: {e}")
        raise HTTPException(status_code=500, detail=str(e))

# 获取APP详情
@router.get("/api/apps/{app_id}")
async def get_app_detail(app_id: str):
    """获取APP详情"""
    try:
        from ...custom_app.app_manager import AppManager
        app_manager = AppManager()
        
        app = app_manager.get_app(app_id)
        if not app:
            raise HTTPException(status_code=404, detail="APP不存在")
        
        return {
            "success": True,
            "app": app
        }
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"获取APP详情失败: {e}")
        raise HTTPException(status_code=500, detail=str(e))

# 创建/更新APP
@router.post("/api/apps/build")
async def build_app(request: AppBuildRequest):
    """构建APP"""
    try:
        from ...custom_app.app_builder import AppBuilder
        app_builder = AppBuilder()
        
        result = await app_builder.build_app(
            app_id=request.app_id,
            name=request.name,
            description=request.description,
            requirements=request.requirements,
            iteration_history=request.iteration_history
        )
        
        return {
            "success": True,
            "app_id": result["app_id"],
            "generated_content": result["content"],
            "message": result["message"]
        }
    except Exception as e:
        logger.error(f"构建APP失败: {e}")
        raise HTTPException(status_code=500, detail=str(e))

# 更新APP内容
@router.put("/api/apps/update")
async def update_app_content(request: AppUpdateRequest):
    """更新APP内容"""
    try:
        from ...custom_app.app_manager import AppManager
        app_manager = AppManager()
        
        success = app_manager.update_app_content(
            app_id=request.app_id,
            page_name=request.page_name,
            content=request.content
        )
        
        if not success:
            raise HTTPException(status_code=404, detail="APP或页面不存在")
        
        return {
            "success": True,
            "message": "APP内容更新成功"
        }
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"更新APP内容失败: {e}")
        raise HTTPException(status_code=500, detail=str(e))

# 更新APP元数据
@router.put("/api/apps/update_metadata")
async def update_app_metadata(request: AppMetadataUpdateRequest):
    """更新APP元数据"""
    try:
        from ...custom_app.app_manager import AppManager
        app_manager = AppManager()
        
        success = app_manager.update_app_metadata(
            app_id=request.app_id,
            name=request.name,
            description=request.description
        )
        
        if not success:
            raise HTTPException(status_code=404, detail="APP不存在")
        
        return {
            "success": True,
            "message": "APP元数据更新成功"
        }
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"更新APP元数据失败: {e}")
        raise HTTPException(status_code=500, detail=str(e))

# 删除APP
@router.delete("/api/apps/{app_id}")
async def delete_app(app_id: str):
    """删除APP"""
    try:
        from ...custom_app.app_manager import AppManager
        app_manager = AppManager()
        
        success = app_manager.delete_app(app_id)
        if not success:
            raise HTTPException(status_code=404, detail="APP不存在")
        
        return {
            "success": True,
            "message": "APP删除成功"
        }
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"删除APP失败: {e}")
        raise HTTPException(status_code=500, detail=str(e))

# 导出APP
@router.get("/api/apps/{app_id}/export")
async def export_app(app_id: str):
    """导出APP为HTML文件"""
    try:
        from ...custom_app.app_manager import AppManager
        app_manager = AppManager()
        
        export_path = app_manager.export_app(app_id)
        if not export_path:
            raise HTTPException(status_code=404, detail="APP不存在")
        
        return FileResponse(
            path=export_path,
            filename=f"{app_id}.zip",
            media_type="application/zip"
        )
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"导出APP失败: {e}")
        raise HTTPException(status_code=500, detail=str(e))

# 导入APP
@router.post("/api/apps/import")
async def import_app(file: UploadFile = File(...)):
    """导入APP"""
    try:
        from ...custom_app.app_manager import AppManager
        app_manager = AppManager()
        
        # 保存上传的文件
        import tempfile
        with tempfile.NamedTemporaryFile(delete=False, suffix=".zip") as tmp_file:
            content = await file.read()
            tmp_file.write(content)
            tmp_file_path = tmp_file.name
        
        try:
            app_id = app_manager.import_app(tmp_file_path)
            return {
                "success": True,
                "app_id": app_id,
                "message": "APP导入成功"
            }
        finally:
            os.unlink(tmp_file_path)
            
    except Exception as e:
        logger.error(f"导入APP失败: {e}")
        raise HTTPException(status_code=500, detail=str(e))

# 获取APP访问日志
@router.get("/api/apps/{app_id}/logs")
async def get_app_logs(app_id: str, limit: int = 100):
    """获取APP访问日志"""
    try:
        from ...custom_app.app_manager import AppManager
        app_manager = AppManager()
        
        logs = app_manager.get_app_logs(app_id, limit=limit)
        return {
            "success": True,
            "logs": logs,
            "count": len(logs)
        }
    except Exception as e:
        logger.error(f"获取APP日志失败: {e}")
        raise HTTPException(status_code=500, detail=str(e))

# APP预览/访问
@router.get("/view/{app_id}")
async def view_app(app_id: str, page: str = "index.html", request: Request = None):
    """访问APP页面"""
    try:
        from ...custom_app.app_manager import AppManager
        app_manager = AppManager()
        
        # 记录访问日志
        if request:
            app_manager.log_access(
                app_id=app_id,
                ip_address=request.client.host,
                user_agent=request.headers.get("user-agent", ""),
                page=page
            )
        
        # 获取APP页面内容
        content = app_manager.get_app_page(app_id, page)
        if not content:
            raise HTTPException(status_code=404, detail="APP页面不存在")
        
        return HTMLResponse(content=content)
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"访问APP失败: {e}")
        raise HTTPException(status_code=500, detail=str(e))

# API代理 - 解决CORS问题
@router.post("/api/proxy")
async def api_proxy(request: Request):
    """API代理服务"""
    try:
        body = await request.json()
        url = body.get("url")
        method = body.get("method", "GET")
        headers = body.get("headers", {})
        data = body.get("data")
        
        if not url:
            raise HTTPException(status_code=400, detail="缺少URL参数")
        
        # 使用现有的API调用器
        from ...agent.api_caller import ApiCaller
        api_caller = ApiCaller()
        
        # 构造API数据格式
        api_data = {
            "method": method.upper(),
            "path": url,
            "req_headers": headers,
            "req_body_other": json.dumps(data) if data else ""
        }
        
        result = api_caller.call_api(api_data=api_data, parameters={})
        
        return {
            "success": True,
            "data": result
        }
        
    except Exception as e:
        logger.error(f"API代理调用失败: {e}")
        raise HTTPException(status_code=500, detail=str(e))
