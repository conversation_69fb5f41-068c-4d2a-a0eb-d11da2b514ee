"""
个性化APP构建器
"""

import json
import os
import asyncio
from typing import List, Dict, Any, Optional
from datetime import datetime
from loguru import logger

from .models import AppMetadata, AppBuildHistory, generate_app_id, validate_app_name, sanitize_html_content
from .app_manager import AppManager
from ..agent.llm_client import LLMClient
from ..vector_db.manager import VectorDBManager


class AppBuilder:
    """APP构建器"""
    
    def __init__(self):
        self.app_manager = AppManager()
        self.llm_client = LLMClient()
        self.vector_db = VectorDBManager()
        
    async def build_app(
        self,
        app_id: Optional[str],
        name: str,
        description: str,
        requirements: str,
        iteration_history: List[Dict[str, Any]] = None
    ) -> Dict[str, Any]:
        """
        构建APP
        
        Args:
            app_id: APP ID（如果是更新现有APP）
            name: APP名称
            description: APP描述
            requirements: 用户需求
            iteration_history: 迭代历史
            
        Returns:
            Dict: 构建结果
        """
        try:
            if not validate_app_name(name):
                raise ValueError("APP名称无效")
            
            # 如果是新APP，创建APP ID
            if not app_id:
                app_id = self.app_manager.create_app(name, description)
                is_new_app = True
            else:
                is_new_app = False
                # 验证APP是否存在
                existing_app = self.app_manager.get_app(app_id)
                if not existing_app:
                    raise ValueError("APP不存在")
            
            # 搜索相关API
            available_apis = await self._search_available_apis(requirements)
            
            # 构建系统提示
            system_prompt = self._build_system_prompt(available_apis)
            
            # 构建用户消息
            user_message = self._build_user_message(
                name, description, requirements, iteration_history
            )
            
            # 调用大模型生成APP
            messages = [
                {"role": "system", "content": system_prompt},
                {"role": "user", "content": user_message}
            ]
            
            # 如果有迭代历史，添加到消息中
            if iteration_history:
                for item in iteration_history[-5:]:  # 只保留最近5次迭代
                    messages.append({"role": "user", "content": item.get("user_request", "")})
                    messages.append({"role": "assistant", "content": item.get("ai_response", "")})
            
            # 调用大模型
            response = await self.llm_client.chat_completion(messages)
            
            if "error" in response:
                raise Exception(f"大模型调用失败: {response['error']}")
            
            # 提取生成的HTML内容
            generated_content = response.get("content", "")
            if not generated_content:
                raise Exception("大模型未返回有效内容")
            
            # 清理和验证HTML内容
            html_content = self._extract_html_content(generated_content)
            html_content = sanitize_html_content(html_content)
            
            # 保存APP内容
            success = self.app_manager.update_app_content(
                app_id=app_id,
                page_name="index.html",
                content=html_content
            )
            
            if not success:
                raise Exception("保存APP内容失败")
            
            # 记录构建历史
            build_history = AppBuildHistory(
                app_id=app_id,
                user_request=requirements,
                ai_response=generated_content,
                generated_content=html_content,
                iteration_number=len(iteration_history) + 1 if iteration_history else 1
            )
            
            self._save_build_history(build_history)
            
            # 更新APP元数据
            if not is_new_app:
                self.app_manager.update_app_metadata(
                    app_id=app_id,
                    name=name,
                    description=description,
                    updated_at=datetime.now().isoformat()
                )
            
            return {
                "app_id": app_id,
                "content": html_content,
                "message": "APP构建成功" if is_new_app else "APP更新成功",
                "available_apis": available_apis
            }
            
        except Exception as e:
            logger.error(f"构建APP失败: {e}")
            raise
    
    async def _search_available_apis(self, requirements: str) -> List[Dict[str, Any]]:
        """搜索可用的API"""
        try:
            # 使用向量数据库搜索相关API
            search_results = self.vector_db.search_apis(requirements, n_results=5)
            
            apis = []
            for result in search_results:
                metadata = result.get("metadata", {})
                api_info = {
                    "api_id": metadata.get("api_id"),
                    "title": metadata.get("title", ""),
                    "method": metadata.get("method", ""),
                    "path": metadata.get("path", ""),
                    "description": result.get("document", ""),
                    "distance": result.get("distance", 1.0)
                }
                apis.append(api_info)
            
            logger.info(f"为需求 '{requirements}' 找到 {len(apis)} 个相关API")
            return apis
            
        except Exception as e:
            logger.error(f"搜索API失败: {e}")
            return []
    
    def _build_system_prompt(self, available_apis: List[Dict[str, Any]]) -> str:
        """构建系统提示"""
        api_list = ""
        if available_apis:
            api_list = "\n\n可用的API接口：\n"
            for i, api in enumerate(available_apis[:5], 1):  # 只显示前5个最相关的API
                api_list += f"{i}. {api['method']} {api['path']} - {api['title']}\n"
                api_list += f"   描述: {api['description'][:100]}...\n"
        
        return f"""你是一个专业的前端开发专家，专门为用户创建个性化的Web应用。

你的任务是根据用户的需求生成完整的HTML页面，包含以下特点：
1. 使用现代化的UI框架（Bootstrap 5）
2. 响应式设计，适配移动端和桌面端
3. 美观的界面设计和良好的用户体验
4. 如果需要调用API，使用提供的API接口
5. 包含必要的JavaScript交互功能
6. 使用Font Awesome图标库

技术要求：
- 使用Bootstrap 5.1.3 CSS框架
- 使用Font Awesome 6.0.0图标库
- JavaScript使用原生JS或jQuery
- 如需调用API，使用fetch或axios
- 所有外部资源使用CDN链接

API调用说明：
- 所有API调用都需要通过代理服务：/apps/api/proxy
- 代理请求格式：POST /apps/api/proxy
- 请求体：{{"url": "实际API地址", "method": "GET/POST/PUT/DELETE", "headers": {{}}, "data": {{}}}}

{api_list}

请生成完整的HTML页面，包含<!DOCTYPE html>声明和完整的HTML结构。
确保代码格式正确，功能完整可用。"""
    
    def _build_user_message(
        self,
        name: str,
        description: str,
        requirements: str,
        iteration_history: Optional[List[Dict[str, Any]]]
    ) -> str:
        """构建用户消息"""
        message = f"""请为我创建一个名为"{name}"的Web应用。

应用描述：{description}

具体需求：{requirements}

"""
        
        if iteration_history:
            message += "\n这是一个迭代更新，之前的版本已经存在。请根据新的需求对现有功能进行改进和完善。\n"
        
        message += """
请生成完整的HTML页面代码，确保：
1. 界面美观，用户体验良好
2. 功能完整，能够满足需求
3. 代码结构清晰，易于维护
4. 如果需要调用API，请使用提供的API接口
5. 包含适当的错误处理和用户反馈

请直接返回HTML代码，不需要额外的解释。"""
        
        return message
    
    def _extract_html_content(self, content: str) -> str:
        """提取HTML内容"""
        # 查找HTML代码块
        import re
        
        # 尝试提取```html代码块
        html_match = re.search(r'```html\s*(.*?)\s*```', content, re.DOTALL | re.IGNORECASE)
        if html_match:
            return html_match.group(1).strip()
        
        # 尝试提取```代码块
        code_match = re.search(r'```\s*(.*?)\s*```', content, re.DOTALL)
        if code_match:
            code_content = code_match.group(1).strip()
            # 检查是否是HTML内容
            if '<!DOCTYPE html>' in code_content or '<html' in code_content:
                return code_content
        
        # 如果没有代码块，查找HTML文档
        if '<!DOCTYPE html>' in content or '<html' in content:
            # 提取从<!DOCTYPE html>或<html>开始到</html>结束的内容
            start_match = re.search(r'(<!DOCTYPE html>|<html[^>]*>)', content, re.IGNORECASE)
            if start_match:
                start_pos = start_match.start()
                end_match = re.search(r'</html>', content[start_pos:], re.IGNORECASE)
                if end_match:
                    end_pos = start_pos + end_match.end()
                    return content[start_pos:end_pos]
        
        # 如果都没找到，返回原内容（可能需要进一步处理）
        return content
    
    def _save_build_history(self, build_history: AppBuildHistory):
        """保存构建历史"""
        try:
            history_file = f"data/apps/logs/{build_history.app_id}_build_history.log"
            with open(history_file, 'a', encoding='utf-8') as f:
                f.write(json.dumps(build_history.to_dict(), ensure_ascii=False) + '\n')
        except Exception as e:
            logger.error(f"保存构建历史失败: {e}")
    
    def get_build_history(self, app_id: str, limit: int = 10) -> List[Dict[str, Any]]:
        """获取构建历史"""
        try:
            history_file = f"data/apps/logs/{app_id}_build_history.log"
            if not os.path.exists(history_file):
                return []
            
            histories = []
            with open(history_file, 'r', encoding='utf-8') as f:
                lines = f.readlines()
                for line in lines[-limit:]:
                    if line.strip():
                        histories.append(json.loads(line.strip()))
            
            return histories
        except Exception as e:
            logger.error(f"获取构建历史失败: {e}")
            return []
