{% extends "base.html" %}

{% block title %}APP构建器 - RestAgent{% endblock %}

{% block extra_css %}
<style>
    .builder-container {
        height: calc(100vh - 100px);
        overflow: hidden;
    }
    .chat-panel {
        height: 100%;
        border-right: 1px solid #dee2e6;
        display: flex;
        flex-direction: column;
    }
    .preview-panel {
        height: 100%;
        display: flex;
        flex-direction: column;
    }
    .chat-messages {
        flex: 1;
        overflow-y: auto;
        padding: 1rem;
        background-color: #f8f9fa;
    }
    .chat-input-area {
        padding: 1rem;
        border-top: 1px solid #dee2e6;
        background-color: white;
    }
    .preview-area {
        flex: 1;
        overflow: hidden;
        position: relative;
        height: 100%;
    }
    .preview-iframe {
        width: 100%;
        height: 100%;
        border: none;
        background-color: white;
    }
    .editor-area {
        flex: 1;
        overflow: hidden;
        position: relative;
        height: 100%;
    }
    .code-editor {
        width: 100%;
        height: 100%;
        border: none;
        font-family: 'Monaco', '<PERSON><PERSON>', 'Ubuntu Mono', monospace;
        font-size: 14px;
        resize: none;
    }
    .message {
        margin-bottom: 1rem;
        padding: 0.75rem;
        border-radius: 0.5rem;
        max-width: 85%;
    }
    .message.user {
        background-color: #e3f2fd;
        margin-left: auto;
        text-align: right;
    }
    .message.assistant {
        background-color: #f5f5f5;
        margin-right: auto;
    }
    .message.system {
        background-color: #fff3cd;
        margin: 0 auto;
        text-align: center;
        font-style: italic;
    }
    .panel-toggle {
        position: absolute;
        top: 10px;
        right: 10px;
        z-index: 1000;
    }
    .toolbar {
        padding: 0.5rem 1rem;
        background-color: #f8f9fa;
        border-bottom: 1px solid #dee2e6;
        display: flex;
        justify-content: between;
        align-items: center;
    }
    .loading-overlay {
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background-color: rgba(255, 255, 255, 0.8);
        display: flex;
        align-items: center;
        justify-content: center;
        z-index: 1000;
        display: none;
    }
    .tab-content {
        height: calc(100% - 60px);
        flex: 1;
        display: flex;
        flex-direction: column;
    }
    .tab-pane {
        flex: 1;
        display: flex;
        flex-direction: column;
    }
    
    /* 浮动按钮样式 */
    #floatingButtons {
        position: fixed;
        top: 50%;
        right: 10px;
        transform: translateY(-50%);
        z-index: 1000;
        display: none;
    }
    
    #floatingButtons .btn {
        display: block;
        margin-bottom: 10px;
        width: 40px;
        height: 40px;
        border-radius: 50%;
        padding: 0;
        display: flex;
        align-items: center;
        justify-content: center;
    }
</style>
{% endblock %}

{% block content %}
<div class="container-fluid p-0">
    <div class="builder-container">
        <div class="row h-100 g-0">
            <!-- 左侧聊天面板 -->
            <div class="col-md-6" id="chatPanel">
                <div class="chat-panel">
                    <!-- 聊天工具栏 -->
                    <div class="toolbar">
                        <div>
                            <h6 class="mb-0">
                                <i class="fas fa-comments me-2"></i>AI助手对话
                            </h6>
                        </div>
                        <div>
                            <button class="btn btn-sm btn-outline-secondary" onclick="clearChat()">
                                <i class="fas fa-trash me-1"></i>清空
                            </button>
                            <button class="btn btn-sm btn-outline-primary" onclick="togglePanel('chat')">
                                <i class="fas fa-eye-slash"></i>
                            </button>
                        </div>
                    </div>
                    
                    <!-- 聊天消息区域 -->
                    <div class="chat-messages" id="chatMessages">
                        <div class="message system">
                            <i class="fas fa-robot me-2"></i>
                            欢迎使用APP构建器！请描述您想要创建的应用功能，我将为您生成相应的界面。
                        </div>
                    </div>
                    
                    <!-- 聊天输入区域 -->
                    <div class="chat-input-area">
                        <form id="chatForm">
                            <div class="input-group">
                                <input type="text" class="form-control" id="chatInput" 
                                       placeholder="描述您想要的APP功能..." required>
                                <button class="btn btn-primary" type="submit" id="sendBtn">
                                    <i class="fas fa-paper-plane"></i>
                                </button>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
            
            <!-- 右侧预览/编辑面板 -->
            <div class="col-md-6" id="previewPanel">
                <div class="preview-panel">
                    <!-- 预览工具栏 -->
                    <div class="toolbar">
                        <ul class="nav nav-tabs border-0" id="previewTabs" role="tablist">
                            <li class="nav-item" role="presentation">
                                <button class="nav-link active" id="info-tab" data-bs-toggle="tab" 
                                        data-bs-target="#info" type="button" role="tab">
                                    <i class="fas fa-info-circle me-1"></i>基本信息
                                </button>
                            </li>
                            <li class="nav-item" role="presentation">
                                <button class="nav-link" id="preview-tab" data-bs-toggle="tab" 
                                        data-bs-target="#preview" type="button" role="tab">
                                    <i class="fas fa-eye me-1"></i>预览
                                </button>
                            </li>
                            <li class="nav-item" role="presentation">
                                <button class="nav-link" id="editor-tab" data-bs-toggle="tab" 
                                        data-bs-target="#editor" type="button" role="tab">
                                    <i class="fas fa-code me-1"></i>编辑
                                </button>
                            </li>
                        </ul>
                        <div>
                            <button class="btn btn-sm btn-outline-success" onclick="saveApp()" id="saveBtn" disabled>
                                <i class="fas fa-save me-1"></i>保存
                            </button>
                            <button class="btn btn-sm btn-outline-primary" onclick="togglePanel('preview')">
                                <i class="fas fa-eye-slash"></i>
                            </button>
                        </div>
                    </div>
                    
                    <!-- 标签页内容 -->
                    <div class="tab-content">
                        <!-- 基本信息标签页 -->
                        <div class="tab-pane fade show active" id="info" role="tabpanel">
                            <div class="p-3">
                                <div class="mb-3">
                                    <label for="appName" class="form-label">APP名称</label>
                                    <input type="text" class="form-control" id="appName" placeholder="请输入APP名称"
                                           onchange="updateAppInfo()">
                                </div>
                                <div class="mb-3">
                                    <label for="appDescription" class="form-label">APP描述</label>
                                    <textarea class="form-control" id="appDescription" rows="3" placeholder="请输入APP描述"
                                              onchange="updateAppInfo()"></textarea>
                                </div>
                            </div>
                        </div>
                        
                        <!-- 预览标签页 -->
                        <div class="tab-pane fade" id="preview" role="tabpanel">
                            <div class="preview-area">
                                <iframe class="preview-iframe" id="previewFrame" 
                                        srcdoc="<div style='display:flex;align-items:center;justify-content:center;height:100vh;color:#6c757d;'><div style='text-align:center;'><i class='fas fa-cube fa-3x mb-3'></i><br>请在左侧描述您的需求<br>AI将为您生成APP界面</div></div>">
                                </iframe>
                                <div class="loading-overlay" id="previewLoading">
                                    <div class="text-center">
                                        <div class="spinner-border text-primary" role="status"></div>
                                        <p class="mt-2">正在生成APP...</p>
                                    </div>
                                </div>
                            </div>
                        </div>
                        
                        <!-- 编辑标签页 -->
                        <div class="tab-pane fade" id="editor" role="tabpanel">
                            <div class="editor-area">
                                <textarea class="code-editor" id="codeEditor" 
                                          placeholder="生成的HTML代码将显示在这里，您可以直接编辑..."></textarea>
                                <div class="position-absolute bottom-0 end-0 p-2">
                                    <button class="btn btn-sm btn-primary" onclick="updatePreview()">
                                        <i class="fas fa-sync me-1"></i>更新预览
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- 浮动按钮，用于显示隐藏的面板 -->
                <div id="floatingButtons" style="position: fixed; top: 50%; right: 10px; z-index: 1000; display: none;">
                    <button class="btn btn-primary mb-2" onclick="showPanel('chat')" title="显示聊天面板">
                        <i class="fas fa-comments"></i>
                    </button>
                    <button class="btn btn-success" onclick="showPanel('preview')" title="显示预览面板">
                        <i class="fas fa-window-maximize"></i>
                    </button>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- 保存成功模态框 -->
<div class="modal fade" id="saveSuccessModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">
                    <i class="fas fa-check-circle text-success me-2"></i>保存成功
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <p>您的APP已成功保存！</p>
                <div class="d-grid gap-2">
                    <button class="btn btn-primary" onclick="viewCurrentApp()">
                        <i class="fas fa-eye me-2"></i>查看APP
                    </button>
                    <button class="btn btn-outline-secondary" onclick="goToAppStore()">
                        <i class="fas fa-store me-2"></i>返回APP商城
                    </button>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
let currentAppId = null;
let chatHistory = [];
let isGenerating = false;

// 页面加载完成后初始化
document.addEventListener('DOMContentLoaded', function() {
    // 获取URL参数中的app_id
    const urlParams = new URLSearchParams(window.location.search);
    currentAppId = urlParams.get('app_id');
    console.log('Initial app ID:', currentAppId);
    
    // 初始化APP信息变量
    window.currentAppName = '';
    window.currentAppDescription = '';
    
    if (currentAppId) {
        loadExistingApp(currentAppId);
    } else {
        showAppInfoForm();
    }
    
    // 绑定事件
    document.getElementById('chatForm').addEventListener('submit', handleChatSubmit);
    document.getElementById('codeEditor').addEventListener('input', function() {
        document.getElementById('saveBtn').disabled = false;
    });
});

// 显示APP信息表单
function showAppInfoForm() {
    // 将APP名称和描述填充到表单字段中
    if (window.currentAppName) {
        document.getElementById('appName').value = window.currentAppName;
    }
    if (window.currentAppDescription) {
        document.getElementById('appDescription').value = window.currentAppDescription;
    }
}

// 加载现有APP
async function loadExistingApp(appId) {
    console.log('Loading existing app with ID:', appId);
    try {
        const response = await fetch(`/apps/api/apps/${appId}`);
        const data = await response.json();
        console.log('Load app response:', data);
        
        if (data.success) {
            const app = data.app;
            console.log('App data:', app);
            // 将APP信息存储在全局变量中
            window.currentAppName = app.name;
            window.currentAppDescription = app.description;
            console.log('Stored app name and description:', window.currentAppName, window.currentAppDescription);
            
            // 加载APP内容
            const content = await loadAppContent(appId);
            console.log('App content:', content);
            if (content) {
                document.getElementById('codeEditor').value = content;
                updatePreview();
            }
            
            // 启用保存按钮，因为已有内容
            document.getElementById('saveBtn').disabled = false;
            showAppInfoForm();
            showAppInfoForm();
        } else {
            showError('加载APP失败');
        }
    } catch (error) {
        console.error('加载APP失败:', error);
        showError('加载APP失败: ' + error.message);
    }
}

// 加载APP内容
async function loadAppContent(appId) {
    try {
        const response = await fetch(`/apps/view/${appId}`);
        if (response.ok) {
            return await response.text();
        }
        return null;
    } catch (error) {
        console.error('加载APP内容失败:', error);
        return null;
    }
}

// 添加更新APP信息的函数
function updateAppInfo() {
    window.currentAppName = document.getElementById('appName').value.trim();
    window.currentAppDescription = document.getElementById('appDescription').value.trim();
    
    // 启用保存按钮如果内容已填写
    if (window.currentAppName && window.currentAppDescription) {
        document.getElementById('saveBtn').disabled = false;
    }
}

// 修改handleChatSubmit函数，移除对currentAppName和currentAppDescription的检查
async function handleChatSubmit(event) {
    event.preventDefault();
    
    if (isGenerating) return;
    
    const chatInput = document.getElementById('chatInput');
    const message = chatInput.value.trim();
    
    if (!message) return;
    
    // 获取最新的APP信息
    const appName = document.getElementById('appName').value.trim();
    const appDescription = document.getElementById('appDescription').value.trim();
    
    // 检查是否需要APP基本信息（仅当没有app_id时才需要）
    if (!currentAppId && (!appName || !appDescription)) {
        alert('请先填写APP名称和描述');
        // 切换到基本信息标签页
        const infoTab = new bootstrap.Tab(document.getElementById('info-tab'));
        infoTab.show();
        return;
    }
    
    // 添加用户消息到聊天
    addChatMessage('user', message);
    chatInput.value = '';
    
    // 开始生成
    isGenerating = true;
    document.getElementById('sendBtn').disabled = true;
    showLoading(true);
    
    try {
        // 构建请求数据
        const requestData = {
            app_id: currentAppId,
            name: appName,
            description: appDescription,
            requirements: message,
            iteration_history: chatHistory
        };
        
        console.log('Sending build request with data:', requestData);
        
        // 调用构建API
        const response = await fetch('/apps/api/apps/build', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify(requestData)
        });
        
        const data = await response.json();
        console.log('Build response:', data);
        
        if (data.success) {
            currentAppId = data.app_id;
            console.log('New app ID:', currentAppId);
            
            // 添加AI响应到聊天
            addChatMessage('assistant', data.message);
            
            // 更新代码编辑器和预览
            document.getElementById('codeEditor').value = data.generated_content;
            updatePreview();
            
            // 启用保存按钮
            document.getElementById('saveBtn').disabled = false;
            
            // 记录聊天历史
            chatHistory.push({
                user_request: message,
                ai_response: data.message,
                generated_content: data.generated_content
            });
            
        } else {
            addChatMessage('system', '生成失败: ' + (data.message || '未知错误'));
        }
        
    } catch (error) {
        console.error('生成APP失败:', error);
        addChatMessage('system', '生成失败: ' + error.message);
    } finally {
        isGenerating = false;
        document.getElementById('sendBtn').disabled = false;
        showLoading(false);
    }
}

// 添加聊天消息
function addChatMessage(type, content) {
    const messagesContainer = document.getElementById('chatMessages');
    const messageDiv = document.createElement('div');
    messageDiv.className = `message ${type}`;
    
    if (type === 'user') {
        messageDiv.innerHTML = `<i class="fas fa-user me-2"></i>${content}`;
    } else if (type === 'assistant') {
        messageDiv.innerHTML = `<i class="fas fa-robot me-2"></i>${content}`;
    } else {
        messageDiv.innerHTML = `<i class="fas fa-info-circle me-2"></i>${content}`;
    }
    
    messagesContainer.appendChild(messageDiv);
    messagesContainer.scrollTop = messagesContainer.scrollHeight;
}

// 更新预览
function updatePreview() {
    const code = document.getElementById('codeEditor').value;
    const previewFrame = document.getElementById('previewFrame');
    
    if (code.trim()) {
        previewFrame.srcdoc = code;
    } else {
        previewFrame.srcdoc = "<div style='display:flex;align-items:center;justify-content:center;height:100vh;color:#6c757d;'><div style='text-align:center;'><i class='fas fa-cube fa-3x mb-3'></i><br>暂无内容</div></div>";
    }
}

// 保存APP
async function saveApp() {
    console.log('saveApp function called');
    if (!currentAppId) {
        alert('请先生成APP内容');
        return;
    }
    
    const code = document.getElementById('codeEditor').value;
    if (!code.trim()) {
        alert('APP内容不能为空');
        return;
    }
    
    const appName = document.getElementById('appName').value.trim();
    const appDescription = document.getElementById('appDescription').value.trim();
    
    if (!appName || !appDescription) {
        alert('请填写APP名称和描述');
        // 切换到基本信息标签页
        const infoTab = new bootstrap.Tab(document.getElementById('info-tab'));
        infoTab.show();
        return;
    }
    
    try {
        // 先更新APP内容（因为这是主要的保存操作）
        const updateContentResponse = await fetch('/apps/api/apps/update', {
            method: 'PUT',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({
                app_id: currentAppId,
                page_name: 'index.html',
                content: code
            })
        });
        
        const contentData = await updateContentResponse.json();
        console.log('Update content response:', contentData);
        
        if (!contentData.success) {
            alert('保存失败: ' + contentData.message);
            return;
        }
        
        // 然后更新APP元数据（名称和描述）
        const updateMetadataResponse = await fetch('/apps/api/apps/update_metadata', {
            method: 'PUT',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({
                app_id: currentAppId,
                name: appName,
                description: appDescription
            })
        });
        
        const metadataData = await updateMetadataResponse.json();
        console.log('Update metadata response:', metadataData);
        
        if (!metadataData.success) {
            alert('更新APP信息失败: ' + metadataData.message);
            return;
        }
        
        // 更新全局变量中的APP名称和描述
        window.currentAppName = appName;
        window.currentAppDescription = appDescription;
        
        document.getElementById('saveBtn').disabled = true;
        new bootstrap.Modal(document.getElementById('saveSuccessModal')).show();
    } catch (error) {
        console.error('保存失败:', error);
        alert('保存失败: ' + error.message);
    }
}

// 清空聊天
function clearChat() {
    if (confirm('确定要清空聊天记录吗？')) {
        document.getElementById('chatMessages').innerHTML = `
            <div class="message system">
                <i class="fas fa-robot me-2"></i>
                欢迎使用APP构建器！请描述您想要创建的应用功能，我将为您生成相应的界面。
            </div>
        `;
        chatHistory = [];
    }
}

// 切换面板显示
function togglePanel(panel) {
    const chatPanel = document.getElementById('chatPanel');
    const previewPanel = document.getElementById('previewPanel');
    const floatingButtons = document.getElementById('floatingButtons');
    
    if (panel === 'chat') {
        if (chatPanel.style.display === 'none') {
            chatPanel.style.display = 'block';
            chatPanel.className = 'col-md-6';
            if (previewPanel.style.display !== 'none') {
                previewPanel.className = 'col-md-6';
            } else {
                previewPanel.className = 'col-md-12';
            }
        } else {
            chatPanel.style.display = 'none';
            if (previewPanel.style.display !== 'none') {
                previewPanel.className = 'col-md-12';
            }
        }
    } else if (panel === 'preview') {
        if (previewPanel.style.display === 'none') {
            previewPanel.style.display = 'block';
            previewPanel.className = 'col-md-6';
            if (chatPanel.style.display !== 'none') {
                chatPanel.className = 'col-md-6';
            } else {
                chatPanel.className = 'col-md-12';
            }
        } else {
            previewPanel.style.display = 'none';
            if (chatPanel.style.display !== 'none') {
                chatPanel.className = 'col-md-12';
            }
        }
    }
    
    // 检查是否需要显示浮动按钮
    if (chatPanel.style.display === 'none' || previewPanel.style.display === 'none') {
        floatingButtons.style.display = 'block';
    } else {
        floatingButtons.style.display = 'none';
    }
}

// 显示指定面板
function showPanel(panel) {
    const chatPanel = document.getElementById('chatPanel');
    const previewPanel = document.getElementById('previewPanel');
    const floatingButtons = document.getElementById('floatingButtons');
    
    if (panel === 'chat') {
        chatPanel.style.display = 'block';
        if (previewPanel.style.display !== 'none') {
            chatPanel.className = 'col-md-6';
            previewPanel.className = 'col-md-6';
        } else {
            chatPanel.className = 'col-md-12';
        }
    } else if (panel === 'preview') {
        previewPanel.style.display = 'block';
        if (chatPanel.style.display !== 'none') {
            chatPanel.className = 'col-md-6';
            previewPanel.className = 'col-md-6';
        } else {
            previewPanel.className = 'col-md-12';
        }
    }
    
    // 检查是否需要隐藏浮动按钮
    if (chatPanel.style.display !== 'none' && previewPanel.style.display !== 'none') {
        floatingButtons.style.display = 'none';
    }
}

// 显示/隐藏加载状态
function showLoading(show) {
    const loading = document.getElementById('previewLoading');
    loading.style.display = show ? 'flex' : 'none';
}

// 显示错误信息
function showError(message) {
    alert('错误: ' + message);
}

// 查看当前APP
function viewCurrentApp() {
    if (currentAppId) {
        window.open(`/apps/view/${currentAppId}`, '_blank');
    }
}

// 返回APP商城
function goToAppStore() {
    window.location.href = '/apps/store';
}
</script>
{% endblock %}
