
实现以下全部功能，中间不用询问，继续完成全部任务并测试验证：
# 功能描述
个性化APP定制功能，用户可以通过与大模型交互定制自己个性化的app界面，达到Vibe coding的能力。具体要求如下：
1. 增加一个“个性化APP”入口菜单，主界面类似应用商城，可显示当前已经创建的各APP，包括名称、描述、图标展示等，用户点击APP可跳转到这个APP界面。
2. 可以通过“创建APP”启动APP的构建界面：
    2.1 用户可以输入请求，后台调用大模型根据用户请求生成前端界面，生成的前端界面通过REST API接口查询、提交数据。
    2.2 调用大模型时，可以把API向量库的搜索能力做为工具告诉大模型，让大模型在生成界面时，可以调用向量数据库进行搜索有哪些API可用。
    2.3 APP对REST API的调用通过本项目的代理执行，用来解决前端UI的CORS 跨域问题.
    2.3 APP构建过程支持迭代， 用户可以在请求框提交新的要求，让大模型根据要求修改、输出新的APP界面。
    2.4 APP构建界面可分为左右2部分，左边是用户和大模型的交互记录，右边可以直接预览和编辑当前生成的APP界面，并且支持在线修改后刷新预览。左边交互记录部分和右边预览/编辑部分都可以分别隐藏。
    2.5 创建的APP，保存到data/html目录下的app子目录，并且能够通过“个性化APP”主界面的链接访问。
    2.6 已经创建的APP，支持再次修改。
    2.7 个性化app可支持多个页面文件
3. 个性化APP管理功能
    - 3.1 可以删除
    - 3.2 后台记录APP的访问日志
    - 3.3 可以导出为html，并支持导入