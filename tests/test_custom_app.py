"""
个性化APP功能测试
"""

import pytest
import os
import json
import tempfile
import shutil
from unittest.mock import Mock, patch, AsyncMock
from fastapi.testclient import TestClient

from src.custom_app.models import AppMetadata, AppPage, AppConfig, AppAccessLog
from src.custom_app.app_manager import AppManager
from src.custom_app.app_builder import AppBuilder


class TestAppModels:
    """测试APP数据模型"""
    
    def test_app_metadata_creation(self):
        """测试APP元数据创建"""
        metadata = AppMetadata(
            name="测试APP",
            description="这是一个测试APP",
            category="测试",
            tags=["test", "demo"]
        )
        
        assert metadata.name == "测试APP"
        assert metadata.description == "这是一个测试APP"
        assert metadata.category == "测试"
        assert "test" in metadata.tags
        assert metadata.version == "1.0.0"
        assert metadata.access_count == 0
        
    def test_app_metadata_to_dict(self):
        """测试APP元数据转换为字典"""
        metadata = AppMetadata(name="测试", description="描述")
        data = metadata.to_dict()
        
        assert isinstance(data, dict)
        assert data["name"] == "测试"
        assert data["description"] == "描述"
        
    def test_app_page_creation(self):
        """测试APP页面创建"""
        page = AppPage(
            name="index.html",
            title="首页",
            content="<html><body>Hello</body></html>"
        )
        
        assert page.name == "index.html"
        assert page.title == "首页"
        assert "Hello" in page.content
        
    def test_app_access_log_creation(self):
        """测试访问日志创建"""
        log = AppAccessLog(
            app_id="test-app",
            ip_address="127.0.0.1",
            user_agent="Test Browser",
            page="index.html"
        )
        
        assert log.app_id == "test-app"
        assert log.ip_address == "127.0.0.1"
        assert log.user_agent == "Test Browser"
        assert log.page == "index.html"


class TestAppManager:
    """测试APP管理器"""
    
    @pytest.fixture
    def temp_dir(self):
        """创建临时目录"""
        temp_dir = tempfile.mkdtemp()
        yield temp_dir
        shutil.rmtree(temp_dir)
    
    @pytest.fixture
    def app_manager(self, temp_dir):
        """创建APP管理器实例"""
        # 修改路径到临时目录
        manager = AppManager()
        manager.apps_config_path = os.path.join(temp_dir, "apps.json")
        manager.apps_dir = os.path.join(temp_dir, "apps")
        manager.logs_dir = os.path.join(temp_dir, "logs")
        
        # 创建目录
        os.makedirs(os.path.dirname(manager.apps_config_path), exist_ok=True)
        os.makedirs(manager.apps_dir, exist_ok=True)
        os.makedirs(manager.logs_dir, exist_ok=True)
        
        manager._init_config()
        return manager
    
    def test_create_app(self, app_manager):
        """测试创建APP"""
        app_id = app_manager.create_app("测试APP", "这是一个测试APP", "测试用户")
        
        assert app_id is not None
        assert len(app_id) > 0
        
        # 验证APP目录是否创建
        app_dir = os.path.join(app_manager.apps_dir, app_id)
        assert os.path.exists(app_dir)
        
        # 验证默认页面是否创建
        index_path = os.path.join(app_dir, "index.html")
        assert os.path.exists(index_path)
        
        # 验证元数据是否保存
        metadata_path = os.path.join(app_dir, "metadata.json")
        assert os.path.exists(metadata_path)
        
    def test_get_apps(self, app_manager):
        """测试获取APP列表"""
        # 创建几个测试APP
        app1_id = app_manager.create_app("APP1", "描述1", "用户1")
        app2_id = app_manager.create_app("APP2", "描述2", "用户2")
        
        apps = app_manager.get_apps()
        assert len(apps) == 2
        
        app_names = [app["name"] for app in apps]
        assert "APP1" in app_names
        assert "APP2" in app_names
        
    def test_get_app(self, app_manager):
        """测试获取单个APP"""
        app_id = app_manager.create_app("测试APP", "描述", "用户")
        
        app = app_manager.get_app(app_id)
        assert app is not None
        assert app["name"] == "测试APP"
        assert app["description"] == "描述"
        assert app["author"] == "用户"
        
    def test_update_app_content(self, app_manager):
        """测试更新APP内容"""
        app_id = app_manager.create_app("测试APP", "描述", "用户")
        
        new_content = "<html><body><h1>更新的内容</h1></body></html>"
        success = app_manager.update_app_content(app_id, "index.html", new_content)
        
        assert success is True
        
        # 验证内容是否更新
        content = app_manager.get_app_page(app_id, "index.html")
        assert "更新的内容" in content
        
    def test_delete_app(self, app_manager):
        """测试删除APP"""
        app_id = app_manager.create_app("测试APP", "描述", "用户")
        
        # 确认APP存在
        assert app_manager.get_app(app_id) is not None
        
        # 删除APP
        success = app_manager.delete_app(app_id)
        assert success is True
        
        # 确认APP已删除
        assert app_manager.get_app(app_id) is None
        
        # 确认目录已删除
        app_dir = os.path.join(app_manager.apps_dir, app_id)
        assert not os.path.exists(app_dir)
        
    def test_log_access(self, app_manager):
        """测试访问日志记录"""
        app_id = app_manager.create_app("测试APP", "描述", "用户")
        
        # 记录访问
        app_manager.log_access(app_id, "127.0.0.1", "Test Browser", "index.html")
        
        # 获取日志
        logs = app_manager.get_app_logs(app_id)
        assert len(logs) == 1
        assert logs[0]["app_id"] == app_id
        assert logs[0]["ip_address"] == "127.0.0.1"
        
    def test_search_apps(self, app_manager):
        """测试搜索APP"""
        # 创建测试APP
        app_manager.create_app("用户管理", "管理用户信息", "开发者")
        app_manager.create_app("数据分析", "分析业务数据", "分析师")
        
        # 搜索测试
        results = app_manager.get_apps(search="用户")
        assert len(results) == 1
        assert results[0]["name"] == "用户管理"
        
        results = app_manager.get_apps(search="数据")
        assert len(results) == 1
        assert results[0]["name"] == "数据分析"


class TestAppBuilder:
    """测试APP构建器"""
    
    @pytest.fixture
    def app_builder(self):
        """创建APP构建器实例"""
        with patch('src.custom_app.app_builder.AppManager') as mock_manager, \
             patch('src.custom_app.app_builder.LLMClient') as mock_llm, \
             patch('src.custom_app.app_builder.VectorDBManager') as mock_vector:
            
            builder = AppBuilder()
            builder.app_manager = mock_manager.return_value
            builder.llm_client = mock_llm.return_value
            builder.vector_db = mock_vector.return_value
            
            return builder
    
    @pytest.mark.asyncio
    async def test_search_available_apis(self, app_builder):
        """测试搜索可用API"""
        # 模拟向量数据库搜索结果
        mock_results = [
            {
                "metadata": {
                    "api_id": "api1",
                    "title": "用户API",
                    "method": "GET",
                    "path": "/api/users"
                },
                "document": "获取用户列表",
                "distance": 0.1
            }
        ]
        app_builder.vector_db.search_apis.return_value = mock_results
        
        apis = await app_builder._search_available_apis("用户管理")
        
        assert len(apis) == 1
        assert apis[0]["api_id"] == "api1"
        assert apis[0]["title"] == "用户API"
        
    def test_build_system_prompt(self, app_builder):
        """测试构建系统提示"""
        apis = [
            {
                "api_id": "api1",
                "title": "用户API",
                "method": "GET",
                "path": "/api/users",
                "description": "获取用户列表"
            }
        ]
        
        prompt = app_builder._build_system_prompt(apis)
        
        assert "前端开发专家" in prompt
        assert "Bootstrap 5" in prompt
        assert "GET /api/users" in prompt
        assert "用户API" in prompt
        
    def test_extract_html_content(self, app_builder):
        """测试提取HTML内容"""
        # 测试HTML代码块
        content_with_html_block = """
        这是一个HTML页面：
        ```html
        <!DOCTYPE html>
        <html>
        <body>Hello World</body>
        </html>
        ```
        """
        
        result = app_builder._extract_html_content(content_with_html_block)
        assert "<!DOCTYPE html>" in result
        assert "Hello World" in result
        
        # 测试普通代码块
        content_with_code_block = """
        ```
        <!DOCTYPE html>
        <html>
        <body>Test</body>
        </html>
        ```
        """
        
        result = app_builder._extract_html_content(content_with_code_block)
        assert "<!DOCTYPE html>" in result
        assert "Test" in result
        
        # 测试直接HTML内容
        direct_html = "<!DOCTYPE html><html><body>Direct</body></html>"
        result = app_builder._extract_html_content(direct_html)
        assert "Direct" in result


class TestCustomAppAPI:
    """测试个性化APP API"""
    
    @pytest.fixture
    def client(self):
        """创建测试客户端"""
        from src.web_ui.app import create_app
        app = create_app()
        return TestClient(app)
    
    def test_app_store_page(self, client):
        """测试APP商城页面"""
        response = client.get("/apps/store")
        assert response.status_code == 200
        assert "个性化APP商城" in response.text
        
    def test_app_builder_page(self, client):
        """测试APP构建器页面"""
        response = client.get("/apps/builder")
        assert response.status_code == 200
        assert "APP构建器" in response.text
        
    @patch('src.custom_app.app_manager.AppManager')
    def test_get_apps_api(self, mock_manager, client):
        """测试获取APP列表API"""
        # 模拟返回数据
        mock_manager.return_value.get_apps.return_value = [
            {
                "id": "test-app",
                "name": "测试APP",
                "description": "测试描述",
                "category": "测试"
            }
        ]
        
        response = client.get("/apps/api/apps")
        assert response.status_code == 200
        
        data = response.json()
        assert data["success"] is True
        assert len(data["apps"]) == 1
        assert data["apps"][0]["name"] == "测试APP"


def test_validate_app_name():
    """测试APP名称验证"""
    from src.custom_app.models import validate_app_name
    
    assert validate_app_name("正常APP名称") is True
    assert validate_app_name("App123") is True
    assert validate_app_name("") is False
    assert validate_app_name("   ") is False
    assert validate_app_name("包含<非法>字符") is False
    assert validate_app_name("a" * 101) is False  # 超长名称


def test_validate_page_name():
    """测试页面名称验证"""
    from src.custom_app.models import validate_page_name
    
    assert validate_page_name("index.html") is True
    assert validate_page_name("about.html") is True
    assert validate_page_name("page") is False  # 没有.html后缀
    assert validate_page_name("") is False
    assert validate_page_name("page<test>.html") is False  # 包含非法字符


def test_sanitize_html_content():
    """测试HTML内容清理"""
    from src.custom_app.models import sanitize_html_content
    
    # 测试移除script标签
    html_with_script = "<div>Hello</div><script>alert('xss')</script>"
    cleaned = sanitize_html_content(html_with_script)
    assert "<script>" not in cleaned
    assert "Hello" in cleaned
    
    # 测试移除事件处理器
    html_with_events = '<div onclick="alert(\'xss\')">Click me</div>'
    cleaned = sanitize_html_content(html_with_events)
    assert "onclick" not in cleaned
    assert "Click me" in cleaned


if __name__ == "__main__":
    pytest.main([__file__])
